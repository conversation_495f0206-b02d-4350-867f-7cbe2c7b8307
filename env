AZURE_OPENAI_ENDPOINT="https://demoriskusecaseopenai.openai.azure.com/"
AZURE_OPENAI_KEY="5QNMjBwua5biqFPLTlQwHzvS4orCUVJWcdL1VtBw83TEM5FwQpYbJQQJ99BFACYeBjFXJ3w3AAABACOGwJqQ"

BING_CONNECTION_NAME="YOUR_BING_CONNECTION_NAME"
PROJECT_CONNECTION_STRING="swedencentral.api.azureml.ms;7dd8c783-ac04-48e4-a052-c4fa0067dc09;rg-demoRiskUseCase;demo-project-risk-usecase"
GPT_ENGINE="gpt-4o-mini"

DATABASE_NAME=ContractTemplatesDB
CONTAINER_NAME=Templates
EMBEDDING_ENGINE=text-embedding-3-small

COSMOS_DB_CONNECTION_STRING="AccountEndpoint=https://cosmosdb-nosql-riskpolicies.documents.azure.com:443/;AccountKey=****************************************************************************************;"
DOC_INTEL_ENDPOINT = "https://tableextractionservice.cognitiveservices.azure.com/"
DOC_INTEL_KEY = "EiJTQCxyEspxEo27XfNp3shsZkuz9roOzUgCnSDs5Kxb32BkGkYSJQQJ99BFACYeBjFXJ3w3AAALACOGnn16"