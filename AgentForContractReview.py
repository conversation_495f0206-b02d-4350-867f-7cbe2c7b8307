import os,json,time
from azure.ai.projects.models import FunctionTool, ToolSet, CodeInterpreterTool
from functions import user_functions
from utils_fr_PolicyReview import _initialize_AIAgentProject_client,_extract_data_from_file

# --- Step 1: Import Your Custom Tool ---
try:
    from ToolsetForPolicyReview import (analyze_contract_for_obligations,
                                       extract_fields_using_template,
                                        analyze_document_and_extract_tables)
    
except ImportError:
    print("Error: Could not import 'analyze_contract_for_obligations' from 'ToolsetForPolicyReview.py'.")
    print("Please ensure the file exists and contains your function.")
    exit()




CONFIG = {
    "INPUT_FILE_PATH": "./2022 Lenses SOW - Fully Executed 1.pdf",
    "GUIDELINES_PATH": "./guidelines_knowledge.json", 
    "CONTRACT_TYPE": "Scope of Work",
    "AGENT_NAME": "multi-tool-contract-analysis-agent",
    "POLLING_INTERVAL": 5
}

project_client,model_deployment_name = _initialize_AIAgentProject_client()



# --- Step 3: Main Agent Logic ---
with project_client:
    # Define a dictionary to map tool names to their functions
    available_tools = {
        "analyze_contract_for_obligations": analyze_contract_for_obligations,
        "extract_fields_using_template": extract_fields_using_template,
        "analyze_document_and_extract_tables":analyze_document_and_extract_tables
    }

    all_functions_tool = FunctionTool(functions=[analyze_contract_for_obligations, extract_fields_using_template,analyze_document_and_extract_tables])
    
    # Initialize the toolset and add the single FunctionTool instance.
    toolset = ToolSet()
    toolset.add(all_functions_tool)

# ------------------------- Old Agent Instructions ---------------------------- # 
#     agent_instructions = """
#    You are a multi-skilled legal contract analysis assistant. You have two tools at your disposal.
#     1. `analyze_contract_for_obligations`: Use this tool when the user asks for a general "policy review" or to "extract all obligations" based on the internal framework.
#     2. `extract_fields_using_template`: Use this tool  when the user explicitly mentions a specific template name (like "MSA", "NDA", "Master Service Agreement" or others) and asks to extract fields from the contract text based on template. You must pass the user's template name as the 'template_query' argument.
#     3. `analyze_document_and_extract_tables`: Use this tool to extract tables from contract and dump the data into excel file.
#     Carefully analyze the user's request to choose the correct tool. Your final response should be the direct JSON output from the tool you use.
#     """

    
    # ---------------------------- New Instructions ---------------------- #
    agent_instructions = """
                        You are an expert legal contract analysis assistant that follows a strict three-step process.

                        TOOL USAGE AND PARAMETERS:
                        - `extract_fields_using_template`: Use this tool first. It requires the 'contract_text' and 'template_query'. To save the output, ensure you call the tool with the `save_to_file=True` parameter.
                        - `analyze_contract_for_obligations`: Use this tool second. It requires the 'contract_text' and 'guidelines_path'. You will find both values in the user's prompt. To save the output, ensure you call the tool with the `save_to_file=True` parameter.
                        - `analyze_document_and_extract_tables`: Use this tool last. It requires the 'pdf_path'. You will find the value in the user's prompt. To save the output, ensure you call it with `save_to_excel=True` and `save_to_json=True`.

                        EXECUTION STRATEGY:
                        1. First, call `extract_fields_using_template` to get metadata.
                        2. Second, call `analyze_contract_for_obligations` to get compliance details.
                        3. Third, call `analyze_document_and_extract_tables` to extract all tables.

                        IMPORTANT: Your final response to the user should be a summary message confirming that all three analyses were completed and the files were saved.
                        
                        """

    
    print(f"Creating agent: '{ CONFIG['AGENT_NAME'] }'...")
    agent = project_client.agents.create_agent(
        model=model_deployment_name,
        name=CONFIG['AGENT_NAME'],
        instructions=agent_instructions,
        toolset=toolset,
    )
    print(f"Agent created, ID: {agent.id}")


    thread = project_client.agents.create_thread()
    print(f"Created thread, ID: {thread.id}")

    contract_full_text = _extract_data_from_file(CONFIG["INPUT_FILE_PATH"])

    try:
        contract_full_text = _extract_data_from_file(CONFIG["INPUT_FILE_PATH"])
        if not contract_full_text or len(contract_full_text.strip()) < 100:
            raise ValueError("Contract text is too short or empty")
    except Exception as e:
        print(f"Error extracting contract text: {e}")
        exit()
    
    # ----------------------- Old User Message to trigger agent ------------- # 
    # user_content = f"""1. Please analyze the contract and extract all obligations based on internal guidelines:\n\n Contract: {contract_full_text}
    #     2. Also Perform template based metadata extraction from the contract text. The template name is '{CONTRACT_TYPE}'.
    #     3. Also Extract tables from the contract text.
    #    """
    user_content = f"""
                        Please perform a comprehensive analysis on the provided contract.

                        INPUTS:
                        - PDF Path for table extraction: {CONFIG["INPUT_FILE_PATH"]}
                        - Guidelines Path for obligation analysis: {CONFIG["GUIDELINES_PATH"]}
                        - Contract Type for template extraction: {CONFIG["CONTRACT_TYPE"]}
                        - Full Contract Text: {contract_full_text}

                        Follow your execution strategy precisely using the provided inputs for each tool.
                    """
    

    message = project_client.agents.create_message(
        thread_id=thread.id,
        role="user",
        content=user_content,
    )
    print("Created user message with contract text in the thread.")

    print("Creating run...")
    run = project_client.agents.create_run(thread_id=thread.id, assistant_id=agent.id)
    print(f"Run created with ID: {run.id}, Status: {run.status}")

    while run.status in ["queued", "in_progress", "requires_action"]:
        if run.status == "requires_action":
            print("Run requires action. Executing tools...")
            tool_outputs = []
            for tool_call in run.required_action.submit_tool_outputs.tool_calls:
                tool_name = tool_call.function.name
                tool_args = json.loads(tool_call.function.arguments)

                print(f"  - Agent wants to call tool: '{tool_name}' with args: {tool_args}")

                
                if tool_name in available_tools:
                    function_to_call = available_tools[tool_name]
                    # The argument name must match the parameter name in the function definition
                    output = function_to_call(**tool_args)
                    print(f"  - Tool output generated.")
                    tool_outputs.append({"tool_call_id": tool_call.id, "output": output})


            print("Submitting tool outputs...")
            run = project_client.agents.submit_tool_outputs_to_run(
                thread_id=thread.id, run_id=run.id, tool_outputs=tool_outputs
            )
            print(f"Tool outputs submitted. Run status: {run.status}")

        # Poll the run status
        time.sleep(5)  # Wait for a few seconds before checking again
        run = project_client.agents.get_run(thread_id=thread.id, run_id=run.id)
        print(f"Polling run status: {run.status}")

    # Process the final results of the run
    if run.status == "failed":
        print(f"\n--- ❌ Run Failed ---")
        if run.last_error:
            print(f"Error Code: {run.last_error.code}")
            print(f"Error Message: {run.last_error.message}")
        print("---------------------\n")
    elif run.status == "completed":
        print("\nFetching final conversation messages...")
        messages = project_client.agents.list_messages(thread_id=thread.id)
        
        # The assistant's final message will now contain the result
        assistant_response = None
        for msg in reversed(messages.data):
            if msg.role == "assistant" and msg.content:
                assistant_response = msg.content[0].text.value
                break
        
        print("\n--- ✅ Agent Extraction Complete ---")
        if assistant_response:
            try:
                # The final response from the agent should be the JSON from our tool.
                parsed_json = json.loads(assistant_response)
                print(json.dumps(parsed_json, indent=4))
            except json.JSONDecodeError:
                print("Final response was not valid JSON. Raw output from assistant:")
                print(assistant_response)
        else:
            print("No final response from the assistant was found.")
        print("----------------------------------\n")
    else:
        print(f"Run ended with an unexpected status: {run.status}")


