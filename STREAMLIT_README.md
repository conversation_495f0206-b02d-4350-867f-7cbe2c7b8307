# Contract Analysis Streamlit Application

A comprehensive web interface for analyzing legal contracts using Azure AI services.

## Features

### 📁 **File Upload Support**
- **Contract Files**: PDF (scanned/digital) and TXT formats
- **Guidelines**: JSON files for obligation extraction rules
- **Template Types**: User-specified contract types (MSA, SOW, NDA, etc.)

### 🔍 **Analysis Capabilities**
1. **Template-Based Field Extraction**: Extract specific fields based on contract type
2. **Obligations Analysis**: Identify and categorize legal obligations
3. **Table Extraction**: Extract tables from PDF documents (PDF only)

### 📊 **Interactive Results Display**
- **Three-Tab Interface**: Separate tabs for each analysis type
- **Progress Tracking**: Real-time progress updates during analysis
- **Smart Notifications**: Contextual warnings and success messages
- **Data Visualization**: Tables, JSON views, and expandable sections

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure Environment
```bash
cp .env.example .env
# Edit .env with your Azure service credentials
```

### 3. Run the Application
```bash
python run_app.py
```

Or directly with Streamlit:
```bash
streamlit run streamlit_app.py
```

### 4. Access the App
Open your browser and navigate to: `http://localhost:8501`

## Usage Guide

### Step 1: Upload Files
1. **Contract File**: Upload your contract in PDF or TXT format
   - PDF files support table extraction
   - TXT files will skip table extraction (with notification)

2. **Template Type**: Enter the contract template type
   - Examples: "MSA", "Statement of Work", "NDA"
   - Input is automatically cleaned (whitespace trimmed)

3. **Guidelines File**: Upload JSON file with obligation extraction rules
   - File validation ensures proper JSON format

### Step 2: Start Analysis
- Click "Start Analysis" button (enabled when all files are uploaded)
- Monitor real-time progress updates
- Wait for completion notification

### Step 3: Review Results
Navigate through three result tabs:

#### 📝 **Template Extraction Tab**
- Displays extracted contract metadata
- Shows fields specific to the contract type
- Formatted as both JSON and table views

#### ⚖️ **Obligations Analysis Tab**
- Lists all identified legal obligations
- Categorized by obligation type
- Expandable sections for detailed view
- Searchable dataframe format

#### 📊 **Tables Tab**
- Shows extracted tables (PDF files only)
- Organized by page number
- Interactive dataframe display
- Table dimensions and metadata

## File Structure

```
├── streamlit_app.py          # Main Streamlit application
├── run_app.py               # Application launcher script
├── AgentForContractReview.py # Core agent logic
├── ToolsetForPolicyReview.py # Analysis tools
├── utils_fr_PolicyReview.py  # Utility functions
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
├── temp_uploads/           # Temporary file storage
└── outputs/               # Analysis results storage
```

## Configuration

### Environment Variables
Create a `.env` file with the following variables:

```env
# Azure OpenAI
AZURE_OPENAI_KEY=your_key
AZURE_OPENAI_ENDPOINT=your_endpoint
GPT_ENGINE=your_deployment

# Document Intelligence
DOC_INTEL_ENDPOINT=your_endpoint
DOC_INTEL_KEY=your_key

# Cosmos DB
COSMOS_DB_CONNECTION_STRING=your_connection_string
DATABASE_NAME=your_database
CONTAINER_NAME=your_container

# AI Projects
PROJECT_CONNECTION_STRING=your_project_connection
```

## Features in Detail

### 🎯 **Smart Input Validation**
- Automatic template name cleaning
- JSON file format validation
- File type verification
- Required field checking

### 📈 **Progress Tracking**
- Step-by-step progress indicators
- Real-time status updates
- Error handling with user feedback
- Completion notifications

### 🎨 **User Interface**
- Clean, professional design
- Responsive layout
- Color-coded status indicators
- Intuitive navigation

### 🔄 **File Handling**
- Secure temporary file storage
- Automatic cleanup
- Support for multiple file formats
- File size validation

## Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Environment Variables Not Set**
   - Ensure `.env` file exists and contains all required variables
   - Check Azure service credentials

3. **File Upload Issues**
   - Verify file formats (PDF/TXT for contracts, JSON for guidelines)
   - Check file size limits
   - Ensure files are not corrupted

4. **Analysis Errors**
   - Check Azure service connectivity
   - Verify API keys and endpoints
   - Review uploaded file content

### Error Messages
The application provides detailed error messages for:
- File validation failures
- Azure service connection issues
- Analysis processing errors
- Invalid input formats

## Advanced Usage

### Custom Guidelines
Create custom obligation extraction rules by modifying the guidelines JSON structure:

```json
{
  "obligation_schema": [
    {
      "category_name": "Custom Category",
      "category_description": "Description of obligations",
      "keywords": ["keyword1", "keyword2"]
    }
  ]
}
```

### Batch Processing
For multiple contracts, use the application iteratively or extend the codebase for batch processing capabilities.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review Azure service documentation
3. Verify environment configuration
4. Check application logs for detailed error information
