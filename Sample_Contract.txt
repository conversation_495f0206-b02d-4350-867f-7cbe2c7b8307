MASTER SERVICES AGREEMENT

This Master Services Agreement ("Agreement") is made and entered into as of June 1, 2025 ("Effective Date"), by and between Innovate Solutions Inc., a corporation organized under the laws of the State of Delaware, with its principal office at 123 Innovation Drive, Wilmington, DE 19801 ("Service Provider"), and Global Tech Enterprises LLC, a limited liability company with its principal place of business at 456 Business Avenue, Suite 500, San Francisco, CA 94105 ("Client").

WHEREAS, Service Provider is in the business of providing information technology consulting and software development services;

WHEREAS, Client desires to retain Service Provider to perform such services as may be agreed upon from time to time;

NOW, THEREFORE, in consideration of the mutual covenants contained herein, the parties agree as follows:

1. SERVICES.
Service Provider shall perform the services and provide the deliverables as described in one or more Statements of Work ("SOW") to be executed by both parties. Each SOW shall be incorporated into this Agreement by reference.

2. TERM OF AGREEMENT.
This Agreement shall commence on the Effective Date and shall continue for an initial term of one (1) year. Thereafter, this Agreement shall automatically renew for successive one-year periods unless either party provides written notice of non-renewal at least sixty (60) days prior to the end of the then-current term.

3. PAYMENT AND INVOICING.
Client shall pay Service Provider the fees set forth in each applicable SOW. Service Provider shall submit monthly invoices to Client for services rendered. All invoices are due and payable within thirty (30) days of receipt (Net 30). Late payments shall accrue interest at a rate of 1.5% per month or the highest rate permitted by law, whichever is lower.

4. TERMINATION.
Either party may terminate this Agreement for cause if the other party materially breaches any provision of this Agreement and fails to cure such breach within thirty (30) days of receiving written notice. Furthermore, Client may terminate this Agreement for convenience at any time by providing Service Provider with sixty (60) days prior written notice. Upon termination, Client shall pay for all services performed up to the effective date of termination.

5. CONFIDENTIALITY.
Each party agrees to hold the other party's Confidential Information in strict confidence and not to disclose such information to any third parties.

6. LIMITATION OF LIABILITY.
IN NO EVENT SHALL EITHER PARTY'S AGGREGATE LIABILITY ARISING OUT OF OR RELATED TO THIS AGREEMENT EXCEED THE TOTAL AMOUNT OF FEES PAID BY CLIENT TO SERVICE PROVIDER IN THE SIX (6) MONTHS PRECEDING THE EVENT GIVING RISE TO THE CLAIM.

7. GOVERNING LAW.
This Agreement and any disputes arising hereunder shall be governed by and construed in accordance with the laws of the State of Delaware, without regard to its conflict of laws principles.


4. Reporting and Communication
4.1. Status Reporting. The Service Provider shall deliver to the Client a detailed project status report via email no later than 5:00 PM EST every Friday for the duration of the engagement. This report shall include, at a minimum, a summary of work completed, milestones achieved, any risks or issues identified, and the planned activities for the following week.

4.2. Project Meetings. The Service Provider will organize and lead a bi-weekly project review meeting via video conference. The agenda for this meeting must be circulated to all attendees at least 24 hours in advance.

7. Data Security and Confidentiality
7.1. Data Protection. The Service Provider must ensure that all Client data is encrypted at rest using the AES-256 encryption standard and in transit using TLS 1.2 or higher. The Service Provider shall restrict access to Client data to only those employees who have a direct need for such access to perform the services outlined herein.

7.2. Security Audits. The Service Provider agrees to conduct an annual third-party security audit and will provide the Client with a copy of the final audit report within 30 days of its completion.

7.3. Breach Notification. In the event of a security breach affecting the Client's data, the Service Provider is obligated to notify the Client's designated security contact in writing within 12 hours of discovering the breach.

8. Service Levels and Performance
8.1. System Availability. The Service Provider guarantees that the hosted platform will maintain a System Availability of at least 99.8% during each calendar month. System Availability will be calculated as (Total Minutes in Month - Downtime Minutes) / (Total Minutes in Month).

8.2. Support Response Time. For any support requests classified as "Urgent," the Service Provider must provide an initial response within one (1) hour of the request being logged in the support portal.

11. Miscellaneous
11.4. Insurance. For the term of this Agreement, the Service Provider shall maintain a policy of general liability insurance with a minimum coverage of $2,000,000 per occurrence.

IN WITNESS WHEREOF, the parties hereto have executed this Agreement as of the Effective Date.

Innovate Solutions Inc.
By: _________________________
Name: Jane Doe
Title: CEO

Global Tech Enterprises LLC
By: _________________________
Name: John Smith
Title: CTO