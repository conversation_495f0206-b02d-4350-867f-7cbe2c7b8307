#!/usr/bin/env python3
"""
Demo data and mock functions for testing the Streamlit app without Azure services
"""

import json
import time
import random

def create_demo_guidelines():
    """Create a demo guidelines JSON file"""
    guidelines = {
        "obligation_schema": [
            {
                "category_name": "Commercial and Financial Management",
                "category_description": "Covers all financial and commercial responsibilities in the contract",
                "keywords": ["payment", "invoice", "fees", "pricing", "discount", "rebate"]
            },
            {
                "category_name": "Service Delivery Management", 
                "category_description": "Focuses on the core delivery of services and deliverables",
                "keywords": ["deliverables", "reports", "services", "performance", "SLA"]
            },
            {
                "category_name": "Compliance and Risk Management",
                "category_description": "Addresses regulatory compliance and risk mitigation requirements",
                "keywords": ["compliance", "audit", "security", "privacy", "confidentiality"]
            }
        ]
    }
    
    with open("demo_guidelines.json", "w") as f:
        json.dump(guidelines, f, indent=2)
    
    print("✅ Created demo_guidelines.json")

def create_demo_contract():
    """Create a demo contract text file"""
    contract_text = """
MASTER SERVICES AGREEMENT

This Master Services Agreement ("Agreement") is made and entered into as of June 1, 2025 ("Effective Date"), by and between Innovate Solutions Inc., a corporation organized under the laws of the State of Delaware, with its principal office at 123 Innovation Drive, Wilmington, DE 19801 ("Service Provider"), and Global Tech Enterprises LLC, a limited liability company with its principal place of business at 456 Business Avenue, Suite 500, San Francisco, CA 94105 ("Client").

WHEREAS, Service Provider is in the business of providing information technology consulting and software development services;

WHEREAS, Client desires to retain Service Provider to perform such services as may be agreed upon from time to time;

NOW, THEREFORE, in consideration of the mutual covenants contained herein, the parties agree as follows:

1. SERVICES.
Service Provider shall perform the services and provide the deliverables as described in one or more Statements of Work ("SOW") to be executed by both parties. Each SOW shall be incorporated into this Agreement by reference.

2. TERM OF AGREEMENT.
This Agreement shall commence on the Effective Date and shall continue for an initial term of one (1) year. Thereafter, this Agreement shall automatically renew for successive one-year periods unless either party provides written notice of non-renewal at least sixty (60) days prior to the end of the then-current term.

3. PAYMENT AND INVOICING.
Client shall pay Service Provider the fees set forth in each applicable SOW. Service Provider shall submit monthly invoices to Client for services rendered. All invoices are due and payable within thirty (30) days of receipt (Net 30). Late payments shall accrue interest at a rate of 1.5% per month or the highest rate permitted by law, whichever is lower.

4. TERMINATION.
Either party may terminate this Agreement for cause if the other party materially breaches any provision of this Agreement and fails to cure such breach within thirty (30) days of receiving written notice. Furthermore, Client may terminate this Agreement for convenience at any time by providing Service Provider with sixty (60) days prior written notice. Upon termination, Client shall pay for all services performed up to the effective date of termination.

5. CONFIDENTIALITY.
Each party agrees to hold the other party's Confidential Information in strict confidence and not to disclose such information to any third parties. This obligation shall survive termination of this Agreement for a period of five (5) years.

6. LIMITATION OF LIABILITY.
IN NO EVENT SHALL EITHER PARTY'S AGGREGATE LIABILITY ARISING OUT OF OR RELATED TO THIS AGREEMENT EXCEED THE TOTAL AMOUNT OF FEES PAID BY CLIENT TO SERVICE PROVIDER IN THE SIX (6) MONTHS PRECEDING THE EVENT GIVING RISE TO THE CLAIM.

7. GOVERNING LAW.
This Agreement and any disputes arising hereunder shall be governed by and construed in accordance with the laws of the State of Delaware, without regard to its conflict of laws principles.

8. PERFORMANCE STANDARDS.
Service Provider shall maintain a service level agreement (SLA) of 99.5% uptime for all critical systems. Monthly performance reports shall be provided to Client within five (5) business days of month-end.

9. COMPLIANCE REQUIREMENTS.
Service Provider shall comply with all applicable laws and regulations, including but not limited to data privacy regulations such as GDPR and CCPA. Annual compliance audits shall be conducted by an independent third party.

10. INTELLECTUAL PROPERTY.
All work product and deliverables created under this Agreement shall be the exclusive property of Client. Service Provider hereby assigns all rights, title, and interest in such work product to Client.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

INNOVATE SOLUTIONS INC.          GLOBAL TECH ENTERPRISES LLC

By: _________________________    By: _________________________
Name: John Smith                 Name: Sarah Johnson  
Title: Chief Executive Officer   Title: Chief Technology Officer
Date: June 1, 2025              Date: June 1, 2025
"""
    
    with open("demo_contract.txt", "w") as f:
        f.write(contract_text)
    
    print("✅ Created demo_contract.txt")

def mock_template_extraction(contract_text, template_query):
    """Mock template extraction function"""
    time.sleep(2)  # Simulate processing time
    
    mock_result = {
        "status": "success",
        "result": {
            "analysis_type": "template_based_extraction",
            "template_used": template_query,
            "timestamp": "2025-06-29T14:30:22",
            "result": {
                "contract_title": "Master Services Agreement",
                "effective_date": "June 1, 2025",
                "service_provider": "Innovate Solutions Inc.",
                "client": "Global Tech Enterprises LLC",
                "initial_term": "1 year",
                "payment_terms": "Net 30",
                "governing_law": "Delaware",
                "termination_notice": "60 days",
                "liability_cap": "6 months of fees",
                "confidentiality_period": "5 years"
            }
        }
    }
    
    return json.dumps(mock_result)

def mock_obligations_analysis(contract_text, guidelines_path):
    """Mock obligations analysis function"""
    time.sleep(3)  # Simulate processing time
    
    mock_obligations = [
        {
            "text": "Service Provider shall submit monthly invoices to Client for services rendered",
            "category": "Commercial and Financial Management",
            "obligation_type": "Reporting",
            "frequency": "Monthly",
            "responsible_party": "Service Provider"
        },
        {
            "text": "All invoices are due and payable within thirty (30) days of receipt",
            "category": "Commercial and Financial Management", 
            "obligation_type": "Payment",
            "deadline": "30 days",
            "responsible_party": "Client"
        },
        {
            "text": "Service Provider shall maintain a service level agreement (SLA) of 99.5% uptime",
            "category": "Service Delivery Management",
            "obligation_type": "Performance Standard",
            "metric": "99.5% uptime",
            "responsible_party": "Service Provider"
        },
        {
            "text": "Monthly performance reports shall be provided to Client within five (5) business days",
            "category": "Service Delivery Management",
            "obligation_type": "Reporting",
            "frequency": "Monthly",
            "deadline": "5 business days",
            "responsible_party": "Service Provider"
        },
        {
            "text": "Service Provider shall comply with all applicable laws and regulations, including GDPR and CCPA",
            "category": "Compliance and Risk Management",
            "obligation_type": "Regulatory Compliance",
            "scope": "Data privacy regulations",
            "responsible_party": "Service Provider"
        },
        {
            "text": "Annual compliance audits shall be conducted by an independent third party",
            "category": "Compliance and Risk Management",
            "obligation_type": "Audit",
            "frequency": "Annual",
            "responsible_party": "Service Provider"
        }
    ]
    
    mock_result = {
        "status": "success",
        "result": {
            "analysis_type": "policy_based_obligations",
            "timestamp": "2025-06-29T14:30:25",
            "total_obligations": len(mock_obligations),
            "result": mock_obligations
        }
    }
    
    return json.dumps(mock_result)

def mock_table_extraction(pdf_path):
    """Mock table extraction function"""
    time.sleep(2)  # Simulate processing time
    
    # Check if it's a PDF file
    if not pdf_path.lower().endswith('.pdf'):
        mock_result = {
            "message": "Table extraction is only available for PDF files",
            "file_type": "text"
        }
        return json.dumps(mock_result)
    
    # Mock table data
    mock_tables = [
        {
            "page_number": 1,
            "tables": [
                {
                    "table_index": 0,
                    "rows": 4,
                    "columns": 3,
                    "data": [
                        ["Service Type", "Monthly Fee", "SLA"],
                        ["Basic Support", "$5,000", "99.0%"],
                        ["Premium Support", "$10,000", "99.5%"],
                        ["Enterprise Support", "$20,000", "99.9%"]
                    ]
                }
            ]
        },
        {
            "page_number": 2,
            "tables": [
                {
                    "table_index": 0,
                    "rows": 3,
                    "columns": 2,
                    "data": [
                        ["Milestone", "Delivery Date"],
                        ["Phase 1 Completion", "July 15, 2025"],
                        ["Phase 2 Completion", "September 30, 2025"]
                    ]
                }
            ]
        }
    ]
    
    mock_result = {
        "status": "success",
        "result": {
            "analysis_type": "table_extraction",
            "source_file": pdf_path,
            "timestamp": "2025-06-29T14:30:27",
            "total_pages": 2,
            "pages_with_tables": 2,
            "tables": mock_tables
        }
    }
    
    return json.dumps(mock_result)

def setup_demo_environment():
    """Set up demo environment with sample files"""
    print("🎭 Setting up demo environment...")
    
    create_demo_guidelines()
    create_demo_contract()
    
    print("\n📁 Demo files created:")
    print("  - demo_contract.txt (sample contract)")
    print("  - demo_guidelines.json (sample guidelines)")
    print("\n🎯 To test the app:")
    print("  1. Upload demo_contract.txt as contract file")
    print("  2. Enter 'MSA' as template type")
    print("  3. Upload demo_guidelines.json as guidelines file")
    print("  4. Click 'Start Analysis'")
    print("\n⚠️  Note: This uses mock data for demonstration purposes")

if __name__ == "__main__":
    setup_demo_environment()
