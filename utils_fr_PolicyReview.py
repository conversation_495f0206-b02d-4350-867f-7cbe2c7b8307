
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from dotenv import load_dotenv
from openai import AzureOpenAI # We need a direct client for the detailed extraction call
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from azure.ai.projects import AIProjectClient
from azure.identity import DefaultAzureCredential


from pathlib import Path

import os,json,pandas as pd


def _initialize_openai_client():

    """Initializes and returns an AzureOpenAI client."""

    load_dotenv()
    try:
        return AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_KEY"),
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
            api_version="2025-01-01-preview"  # Using a stable API version
        )
    except Exception as e:
        # This will be caught by the calling function's error handling
        raise ConnectionError(f"Failed to initialize OpenAI client for tool: {e}")
    


def _initialize_document_intelligence_client():
   
    """Initializes and returns a Document Intelligence client using environment variables."""
    load_dotenv()
    
    try:
    
       return DocumentIntelligenceClient(endpoint=os.getenv("DOC_INTEL_ENDPOINT"), 
                                         credential=AzureKeyCredential(os.getenv("DOC_INTEL_KEY")))

    
    except Exception as e:
        # This will be caught by the calling function's error handling
        raise ConnectionError(f"Failed to initialize Document Intelligence client for tool: {e}")
    

def _initialize_AIAgentProject_client():

    """ Initializes and returns a Azure AI Aent client using environment variables. """

    load_dotenv()

    try:
        model_deployment_name = os.getenv("GPT_ENGINE")
        project_connection_string = os.getenv("PROJECT_CONNECTION_STRING")

        if not all([model_deployment_name, project_connection_string]):
            raise ValueError("Error: Please set GPT_ENGINE and PROJECT_CONNECTION_STRING in your .env file.")


        return AIProjectClient.from_connection_string(
            credential=DefaultAzureCredential(),
            conn_str=project_connection_string,
        ),model_deployment_name
    
    except Exception as e:
        # This will be caught by the calling function's error handling
        raise ConnectionError(f"Failed to initialize Azure AI Aent client for tool: {e}")
    



def _validate_file_path(file_path: str) -> bool:
    """Validates if file exists and is accessible."""
    if not file_path or not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")
    return True


def _create_error_response(error_message: str, success: bool = False) -> str:
    """Creates a standardized error response."""
    return json.dumps({
        "error": error_message,
        "success": success,
        "data": None
    })

def _create_success_response(data: any, message: str = "Operation completed successfully") -> str:
    """Creates a standardized success response."""
    return json.dumps({
        "success": True,
        "message": message,
        "data": data
    })


def _extract_data_from_file(file_path: str) -> str:
    """
    Extracts text from various file types:
    - PDF files (scanned or digital) using Azure Document Intelligence
    - Text files (.txt, .md, etc.) by reading directly
    
    Args:
        file_path (str): Path to the file to extract text from
        
    Returns:
        str: JSON string containing the extracted text and metadata
    """
    print(f"--- Extracting text from file: {file_path} ---")
    
    try:
        _validate_file_path(file_path)
        
        file_extension = Path(file_path).suffix.lower()
        
        if file_extension == '.pdf':
            # Extract text from PDF using Azure Document Intelligence
            return _extract_text_from_scanned_pdf(file_path)
        
        elif file_extension in ['.txt', '.md', '.csv', '.json']:
            # Extract text from text-based files
            return _extract_text_from_text_file(file_path)
        
        else:
            return _create_error_response(f"Unsupported file type: {file_extension}")
            
    except Exception as e:
        return _create_error_response(f"Error extracting data from file: {e}")


def _extract_text_from_scanned_pdf(pdf_path):
    """
    Uses Azure Document Intelligence's "prebuilt-read" model to OCR a scanned PDF
    and extract all its text content.

    Args:
        pdf_path (str): The path to the PDF file.

    Returns:
        str: The extracted text content of the entire document, or None if an error occurs.
    """

    try:
        if not os.path.exists(pdf_path):
            print(f"Error: The file '{pdf_path}' was not found.")
            return None

        print(f"Analyzing document: {pdf_path}...")
        document_intelligence_client = _initialize_document_intelligence_client()

        # Open the PDF file in binary read mode
        with open(pdf_path, "rb") as f:
            # Use the "prebuilt-read" model for optimized OCR
            poller = document_intelligence_client.begin_analyze_document(
                "prebuilt-read",
                body=f,
                content_type="application/octet-stream"
            )
        
        # Get the result of the analysis
        result = poller.result()
        print("Analysis complete.")

        # Extract metadata
        metadata = {
            "file_type": "PDF",
            "pages": len(result.pages) if result.pages else 0,
            "file_path": pdf_path
        }

        # The 'content' attribute contains the full text extracted from the document
        return _create_success_response({
                "text": result.content,
                "metadata": metadata
            }, "Text successfully extracted from PDF")

    except Exception as e:
        return _create_error_response(f"Error extracting text from PDF: {e}")
    

def _extract_text_from_text_file(file_path: str) -> str:
    """Extract text from text-based files."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        metadata = {
            "file_type": "TEXT",
            "file_size": os.path.getsize(file_path),
            "file_path": file_path
        }
        
        return _create_success_response({
            "text": content,
            "metadata": metadata
        }, "Text successfully extracted from text file")
        
    except Exception as e:
        return _create_error_response(f"Error reading text file: {e}")
    

def _generate_embeddings(text: str, client: AzureOpenAI) -> list:

    """Generates vector embeddings for a given text using Azure OpenAI. (No changes needed here)"""

    model = os.getenv("EMBEDDING_ENGINE")
    if not model:
        raise ValueError("EMBEDDING_ENGINE environment variable not set.")
    response = client.embeddings.create(input=[text], model=model)
    return response.data[0].embedding

def _execute_extraction_call(system_prompt: str, user_prompt: str, client: AzureOpenAI) -> str:

    """Constructs the payload and executes the chat completion call to the LLM."""

    gpt_model = os.getenv("GPT_ENGINE")

    if not gpt_model:
        raise ValueError("GPT_ENGINE environment variable not set.")
    
    try:
        response = client.chat.completions.create(
            model=gpt_model,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.1
        )
        # The tool returns the result as a string, as required by the agent framework
        return response.choices[0].message.content
    
    except Exception as e:

        return _create_error_response(f"An error occurred during LLM extraction: {e}")
      
        



def _save_tables_to_excel(pages_with_tables, excel_path):
    
    """
    Saves extracted tables to a formatted Excel file with one table per sheet.
    Enhanced with proper error handling and empty table management.
    """
    
    if not pages_with_tables:
        print("No tables found to save.")
        # Create a summary sheet indicating no tables found
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                summary_df = pd.DataFrame({
                    'Status': ['No tables found in the document'],
                    'Message': ['The document was processed but no tabular data was detected']
                })
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                print(f"Created summary file at {excel_path}")
        except Exception as e:
            print(f"Error creating summary file: {e}")
            raise
        return

    print(f"Saving tables to {excel_path}...")
    
    # Pre-validate tables to ensure at least one valid table exists
    valid_tables_found = False
    for page_data in pages_with_tables:
        if page_data.get('tables'):
            for table_data in page_data['tables']:
                if table_data and len(table_data) > 0:
                    valid_tables_found = True
                    break
        if valid_tables_found:
            break
    
    if not valid_tables_found:
        print("No valid table data found to save.")
        # Create a summary sheet
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                summary_df = pd.DataFrame({
                    'Status': ['Tables detected but no valid data'],
                    'Message': ['Table structures were found but contained no extractable data'],
                    'Pages_Processed': [len(pages_with_tables)]
                })
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                print(f"Created summary file at {excel_path}")
        except Exception as e:
            print(f"Error creating summary file: {e}")
            raise
        return

    try:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            sheets_created = 0
            
            for page_data in pages_with_tables:
                page_number = page_data.get('page_number', 'Unknown')
                tables = page_data.get('tables', [])
                
                for table_index, table_data in enumerate(tables):
                    sheet_name = f"Page_{page_number}_Table_{table_index + 1}"
                    
                    # Validate table data
                    if not table_data or len(table_data) == 0:
                        print(f"  - Skipping empty table on page {page_number}")
                        continue
                        
                    # Check if table has meaningful content
                    if len(table_data) == 1 and not any(str(cell).strip() for cell in table_data[0]):
                        print(f"  - Skipping table with empty header on page {page_number}")
                        continue

                    try:
                        # Prepare DataFrame
                        header = table_data[0] if table_data else []
                        data = table_data[1:] if len(table_data) > 1 else []
                        
                        # Handle duplicate column names
                        sane_header = []
                        counts = {}
                        for col in header:
                            col_str = str(col).strip() if col else f"Column_{len(sane_header)}"
                            if not col_str:
                                col_str = f"Column_{len(sane_header)}"
                            
                            cur_count = counts.get(col_str, 0)
                            final_col_name = f"{col_str}_{cur_count}" if cur_count > 0 else col_str
                            sane_header.append(final_col_name)
                            counts[col_str] = cur_count + 1

                        # Create DataFrame with proper error handling
                        if not data:
                            # Create empty DataFrame with headers
                            df = pd.DataFrame(columns=sane_header)
                        else:
                            df = pd.DataFrame(data, columns=sane_header)
                        
                        # Ensure sheet name is valid (Excel has 31 char limit)
                        if len(sheet_name) > 31:
                            sheet_name = f"P{page_number}_T{table_index + 1}"
                        
                        # Write the DataFrame to a new sheet
                        df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=1)
                        sheets_created += 1
                        print(f"  - Wrote table to sheet: '{sheet_name}' ({len(df)} rows)")

                        # Apply formatting
                        worksheet = writer.sheets[sheet_name]
                        
                        # Auto-fit column widths
                        for col_idx, column_cells in enumerate(worksheet.columns, 1):
                            max_length = 0
                            column_letter = get_column_letter(col_idx)
                            for cell in column_cells:
                                try:
                                    cell_value = str(cell.value) if cell.value is not None else ""
                                    if len(cell_value) > max_length:
                                        max_length = len(cell_value)
                                except:
                                    pass
                            adjusted_width = min(max(max_length + 2, 10), 50)  # Min 10, Max 50
                            worksheet.column_dimensions[column_letter].width = adjusted_width

                        # Format the header row (row 2, since we start at row 1)
                        if len(df.columns) > 0:
                            header_font = Font(bold=True, color="FFFFFF")
                            header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
                            header_alignment = Alignment(horizontal='center', vertical='center')

                            for cell in worksheet[2]:  # Header row
                                cell.font = header_font
                                cell.fill = header_fill
                                cell.alignment = header_alignment

                            # Freeze the header row and add a title
                            worksheet.freeze_panes = 'A3'
                            worksheet['A1'] = f"Table from Page {page_number}"
                            worksheet['A1'].font = Font(bold=True, size=14)

                            # Add AutoFilter if there's data
                            if len(df) > 0:
                                worksheet.auto_filter.ref = worksheet.dimensions
                        
                    except Exception as table_error:
                        print(f"  - Error processing table on page {page_number}: {table_error}")
                        continue
            
            # Ensure at least one sheet was created
            if sheets_created == 0:
                print("No sheets were successfully created. Adding summary sheet.")
                summary_df = pd.DataFrame({
                    'Status': ['Processing completed but no valid tables saved'],
                    'Reason': ['All detected tables were empty or contained invalid data'],
                    'Pages_Processed': [len(pages_with_tables)]
                })
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                sheets_created = 1
        
        print(f"Successfully saved Excel file with {sheets_created} sheet(s).")
        
    except Exception as e:
        print(f"Error saving Excel file: {e}")
        # Create a fallback file with error info
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                error_df = pd.DataFrame({
                    'Error': [str(e)],
                    'Status': ['Failed to process tables'],
                    'Timestamp': [pd.Timestamp.now()]
                })
                error_df.to_excel(writer, sheet_name='Error_Log', index=False)
        except:
            pass
        raise


