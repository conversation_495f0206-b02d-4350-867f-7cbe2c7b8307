import os
import json
from dotenv import load_dotenv
from azure.cosmos import CosmosClient

from pathlib import Path


import pandas as pd
from azure.core.credentials import AzureKeyCredential
from azure.ai.documentintelligence import DocumentIntelligenceClient
from utils_fr_PolicyReview import (_initialize_openai_client,
                   _initialize_document_intelligence_client,   
                   _execute_extraction_call,
                   _create_success_response,
                   _create_error_response,
                   _generate_embeddings,
                   _validate_file_path,
                   _save_tables_to_excel)





# ==============================================================================
# TOOL 1: Policy-Based Obligation Analysis
# ==============================================================================


# This function encapsulates our core extraction logic
def analyze_contract_for_obligations(contract_text: str, guidelines_path: str,save_to_file: bool = True, output_dir: str = None) -> str:
    """
    Analyzes a given contract text to extract and categorize obligations based on a predefined guidelines or frameworks.

    :param contract_text: The full text of the contract to be analyzed.
    
    :save_to_file (bool): Whether to save results to a JSON file

    :output_dir (str): Directory to save the output files

    Returns:
        str: JSON string representing the extracted obligations and metadata
    """


    print("--- Tool 'analyze_contract_for_obligations' has been activated. ---")
    
    # 1. Load the "Distilled Knowledge" from your guidelines
    try:
        with open(guidelines_path, "r") as f:
            guidelines = json.load(f)

        # Initialize the OpenAI client
        openai_client = _initialize_openai_client()
        
        # 3. Construct the detailed prompts
        system_prompt = f"""
        You are a world-class legal analyst. Your sole purpose is to identify and categorize obligations within a legal agreement based on a strict framework.

        You MUST use the following knowledge to guide your analysis:
        --- BEGIN FRAMEWORK ---
        {json.dumps(guidelines['obligation_schema'], indent=2)}
        --- END FRAMEWORK ---

        Your task is to review the contract text I provide and extract every single obligation you can find. For each obligation, you must:
        1. Quote the exact text from the contract that states the obligation.
        2. Assign it to one of the predefined categories from the framework.
        3. If an obligation does not clearly fit any category, classify it as 'Uncategorized'.
        4. Provide your output as a single, valid JSON array of objects.
        5. If Required, Extract any further information based on guidelines.
        """

        user_prompt = f"""
        Please perform the extraction on the following contract text:

        --- BEGIN CONTRACT ---
        {contract_text}
        --- END CONTRACT ---
        """
        
        # Execute the extraction call using the helper function
        result =   _execute_extraction_call(system_prompt, user_prompt, openai_client)

        print(result)

        # Parse the result to add metadata
        try:
            parsed_result = json.loads(result)
            final_result = {
                "analysis_type": "policy_based_obligations",
                "timestamp": pd.Timestamp.now().isoformat(),
                "result": parsed_result
            }

            if not output_dir:
                output_dir = "."                 


            # Save to file if requested
            if save_to_file:
                    
                    output_path = os.path.join(output_dir,f"obligations_analysis_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json")
                    
                    with open(output_path, 'w') as f:
                        json.dump(final_result, f, indent=2)

                    print(f"Obligations analysis saved to: {output_path}")

            return _create_success_response(final_result, "Obligations analysis completed successfully !!")
        
                 
        except json.JSONDecodeError:
            return _create_error_response("Failed to parse LLM response as JSON")


 

    except FileNotFoundError:
        return _create_error_response("Guideline knowledge file not found.")
    except Exception as e:
        return _create_error_response(f"Error in obligations analysis: {e}")

    



# ==============================================================================
# TOOL 2: Template-Based Field Extraction
# ==============================================================================

def extract_fields_using_template(contract_text: str, template_query: str, save_to_file: bool = True, output_dir: str = None) -> str:
    """
    Extracts specific fields from a contract text based on a dynamically retrieved template
    from a Cosmos DB vector database. Use this when a user specifies a template name.

    Also 

    Args:
        contract_text (str): The full text of the contract to be analyzed
        template_query (str): The name of the template to search for (e.g., 'MSA', 'NDA')
        save_to_file (bool): Whether to save results to a JSON file
        output_dir (str): Directory to save output files

    Returns:
        str: JSON string of the extracted fields and their values
        
    """
    print(f"--- Tool 'extract_fields_using_template' activated with query: '{template_query}' ---")


    # 1. Initialize Clients (Cosmos DB and OpenAI)
    try:
        """
        Initializes and returns the Cosmos DB client, database, and a container specifically for templates.
        """
        
        # Initialize Clients (Cosmos DB and OpenAI)
        openai_client = _initialize_openai_client()
        cosmos_client = CosmosClient.from_connection_string(os.getenv("COSMOS_DB_CONNECTION_STRING"))
        database = cosmos_client.get_database_client(os.getenv("DATABASE_NAME"))
        container = database.get_container_client(os.getenv("CONTAINER_NAME")) # Specific container for templates

    except Exception as e:
        return json.dumps({"error": f"Failed to initialize clients: {e}"})

    # 2. Retrieve the best matching template from Cosmos DB
    try:
        print(f"Searching for template matching '{template_query}' in Cosmos DB...")
        query_vector = _generate_embeddings(template_query, openai_client)
        
        query_text = f"""
        SELECT TOP 1 c.id, c.contract_type, c.system_prompt, c.fields_to_extract
        FROM c
        ORDER BY VectorDistance(c.vector, {query_vector})
        """
        
        results = list(container.query_items(query=query_text, enable_cross_partition_query=True))
        
        if not results:
            return  _create_error_response(f"No template found matching the query: '{template_query}'")
            
        retrieved_template = results[0]
        print(f"Successfully retrieved template: '{retrieved_template['contract_type']}'")
        print("#------------ TEMPLATE ------------------------#")
        print(f"'{retrieved_template}'")
        print("#------------- END ----------------------------#")


    except Exception as e:
        return json.dumps({"error": f"Failed to retrieve template from Cosmos DB: {e}"})

    # 3. Use the retrieved template to extract data from the contract
    try:
        print("Constructing prompt and calling LLM for template-based extraction...")
        system_prompt = retrieved_template["system_prompt"]
        fields_list = "\n".join([f"- {field['field_name']}: {field['prompt']}" for field in retrieved_template["fields_to_extract"]])
        
        user_prompt = f"""
        Please extract the following fields based on their definitions:
        {fields_list}

        The output must be a single, valid JSON object where the keys are the field names.

        --- BEGIN CONTRACT TEXT ---
        {contract_text}
        --- END CONTRACT TEXT ---
        """
        
        # Execute the extraction call using the helper function
        result = _execute_extraction_call(system_prompt, user_prompt, openai_client)

        print(result)

        # Parse and add metadata
        try:
            parsed_result = json.loads(result)
            final_result = {
                "analysis_type": "template_based_extraction",
                "template_used": retrieved_template['contract_type'],
                "template_id": retrieved_template['id'],
                "timestamp": pd.Timestamp.now().isoformat(),
                "result": parsed_result
            }

            # Save outputs
            if not output_dir:
                output_dir = "."

            # Save to file if requested
            if save_to_file:
                    
                    output_path = os.path.join(output_dir,f"template_extraction_{template_query}_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json")
                    
                    with open(output_path, 'w') as f:
                        json.dump(final_result, f, indent=2)
                    
                    print(f"Template extraction saved to: {output_path}")
                
            return _create_success_response(final_result, "Template-based extraction completed successfully")
            
        except json.JSONDecodeError:
                return _create_error_response("Failed to parse LLM response as JSON")

    except Exception as e:
        
        return _create_error_response(f"Error in template extraction: {e}")





## ------------------------------ Table Extraction -------------------------- ##

def analyze_document_and_extract_tables(pdf_path: str,save_to_excel: bool = True, save_to_json: bool = True, output_dir: str = None) -> str:
    
    """
    Analyzes a PDF document and extracts tables page by page.
    
    Args:
        pdf_path (str): Path to the PDF file
        save_to_excel (bool): Whether to save tables to Excel file
        save_to_json (bool): Whether to save tables to JSON file
        output_dir (str): Directory to save output files
        
    Returns:
        str: JSON string containing extracted tables with page numbers
    """

    print(f"--- Analyzing document for tables: {pdf_path} ---")

        
    try:
        _validate_file_path(pdf_path)
        document_intelligence_client = _initialize_document_intelligence_client()


        # Open the PDF file
        with open(pdf_path, "rb") as f:
            poller = document_intelligence_client.begin_analyze_document(
                "prebuilt-layout", body=f, content_type="application/octet-stream"
            )

        result = poller.result()
        extracted_data = []

        # Iterate through each page in the document
        if result.pages:
            print(f"Document has {len(result.pages)} page(s).")
            for page_num, page in enumerate(result.pages):
                page_data = {
                    "page_number": page.page_number,
                    "tables": []
                }

                # Check if there are any tables on the page
                if result.tables:

                    for table_idx, table in enumerate(result.tables):
                        # Check if the table is on the current page by looking at its bounding regions
                        table_on_this_page = any(
                            region.page_number == page.page_number for region in table.bounding_regions
                        )
                        if table_on_this_page:
                            table_content = []
                            # Create a list of lists to represent the table
                            for row in range(table.row_count):
                                row_data = []
                                for col in range(table.column_count):
                                    cell = next(
                                        (
                                            c
                                            for c in table.cells
                                            if c.row_index == row and c.column_index == col
                                        ),
                                        None,
                                    )
                                    row_data.append(cell.content if cell else "")
                                table_content.append(row_data)

                            page_data["tables"].append({
                                "table_index": table_idx,
                                "rows": table.row_count,
                                "columns": table.column_count,
                                "data": table_content
                            })


                if page_data["tables"]:
                    extracted_data.append(page_data)
                    print(f"  - Found {len(page_data['tables'])} table(s) on page {page.page_number}.")

        # Prepare final result
        final_result = {
            "analysis_type": "table_extraction",
            "source_file": pdf_path,
            "timestamp": pd.Timestamp.now().isoformat(),
            "total_pages": len(result.pages) if result.pages else 0,
            "pages_with_tables": len(extracted_data),
            "tables": extracted_data
        }

        # Save outputs
        if not output_dir:
            output_dir = "."
        
        base_filename = Path(pdf_path).stem

         # Save to JSON
        if save_to_json:
            json_path = os.path.join(output_dir, f"{base_filename}_tables_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(json_path, 'w') as f:
                json.dump(final_result, f, indent=2)
            print(f"Tables data saved to JSON: {json_path}")

        # Save to Excel
        if save_to_excel and extracted_data:
            excel_path = os.path.join(output_dir, f"Table Extraction From Contract_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
            _save_tables_to_excel(extracted_data, excel_path)
            final_result["excel_output"] = excel_path

        return _create_success_response(final_result, "Table extraction completed successfully")
    
    except Exception as e:
        return _create_error_response(f"Error in table extraction: {e}")

