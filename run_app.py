#!/usr/bin/env python3
"""
Script to run the Streamlit Contract Analysis Application
"""

import subprocess
import sys
import os

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_imports = [
        ('streamlit', 'streamlit'),
        ('pandas', 'pandas'),
        ('azure.ai.projects', 'azure-ai-projects'),
        ('azure.ai.documentintelligence', 'azure-ai-documentintelligence'),
        ('azure.cosmos', 'azure-cosmos'),
        ('openai', 'openai'),
        ('openpyxl', 'openpyxl')
    ]

    missing_packages = []

    for import_name, package_name in required_imports:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False

    print("✅ All required packages are installed")
    return True

def create_temp_directories():
    """Create necessary temporary directories"""
    directories = ['temp_uploads', 'outputs']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")

def run_streamlit_app():
    """Run the Streamlit application"""
    print("🚀 Starting Contract Analysis Streamlit App...")
    print("📱 The app will open in your default browser")
    print("🔗 URL: http://localhost:8501")
    print("\n" + "="*50)
    print("Press Ctrl+C to stop the application")
    print("="*50 + "\n")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {e}")

def main():
    """Main function"""
    print("🔍 Contract Analysis AI Agent - Streamlit App")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Create necessary directories
    create_temp_directories()
    
    # Run the app
    run_streamlit_app()

if __name__ == "__main__":
    main()
