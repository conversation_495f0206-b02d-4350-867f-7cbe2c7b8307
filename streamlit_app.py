#!/usr/bin/env python3
"""
Streamlit Application for Contract Analysis using Azure AI Agent
"""

import streamlit as st
import os
import json
import tempfile
import time
from pathlib import Path
import pandas as pd
from datetime import datetime

# Import your existing modules
from utils_fr_PolicyReview import _extract_data_from_file, _initialize_AIAgentProject_client
from ToolsetForPolicyReview import (
    analyze_contract_for_obligations,
    extract_fields_using_template,
    analyze_document_and_extract_tables
)

# Page configuration
st.set_page_config(
    page_title="Contract Analysis AI Agent",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .upload-section {
        background-color: #f0f2f6;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 1rem;
    }
    .progress-section {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #1f77b4;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }
    .warning-message {
        background-color: #fff3cd;
        color: #856404;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        border: 1px solid #f5c6cb;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'analysis_complete' not in st.session_state:
        st.session_state.analysis_complete = False
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = {}
    if 'uploaded_files' not in st.session_state:
        st.session_state.uploaded_files = {}
    if 'analysis_in_progress' not in st.session_state:
        st.session_state.analysis_in_progress = False

def validate_json_file(uploaded_file):
    """Validate if uploaded file is a valid JSON"""
    try:
        content = uploaded_file.read()
        json.loads(content)
        uploaded_file.seek(0)  # Reset file pointer
        return True, "Valid JSON file"
    except json.JSONDecodeError as e:
        return False, f"Invalid JSON file: {str(e)}"
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def save_uploaded_file(uploaded_file, directory="temp_uploads"):
    """Save uploaded file to temporary directory"""
    if not os.path.exists(directory):
        os.makedirs(directory)
    
    file_path = os.path.join(directory, uploaded_file.name)
    with open(file_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    return file_path

def clean_template_name(template_name):
    """Clean template name by removing extra whitespaces"""
    if template_name:
        return template_name.strip()
    return ""

def display_progress_update(message, step_num, total_steps):
    """Display progress update with step information"""
    progress_percentage = step_num / total_steps
    st.progress(progress_percentage)
    st.markdown(f"""
    <div class="progress-section">
        <strong>Step {step_num}/{total_steps}:</strong> {message}
    </div>
    """, unsafe_allow_html=True)

def run_contract_analysis(contract_path, template_name, guidelines_path):
    """Run the complete contract analysis pipeline"""
    results = {}
    
    # Extract contract text
    progress_placeholder = st.empty()
    
    with progress_placeholder.container():
        display_progress_update("Extracting contract text...", 1, 4)
        time.sleep(1)
    
    try:
        contract_text = _extract_data_from_file(contract_path)
        if not contract_text or len(contract_text.strip()) < 100:
            raise ValueError("Contract text is too short or empty")
    except Exception as e:
        st.error(f"Error extracting contract text: {e}")
        return None
    
    # Step 1: Template-based extraction
    with progress_placeholder.container():
        display_progress_update("Performing template-based field extraction...", 2, 4)
        time.sleep(1)
    
    try:
        template_result = extract_fields_using_template(
            contract_text=contract_text,
            template_query=template_name,
            save_to_file=True,
            output_dir="temp_uploads"
        )
        results['template_extraction'] = json.loads(template_result)
    except Exception as e:
        st.error(f"Error in template extraction: {e}")
        results['template_extraction'] = {"error": str(e)}
    
    # Step 2: Obligations analysis
    with progress_placeholder.container():
        display_progress_update("Analyzing contract obligations...", 3, 4)
        time.sleep(1)
    
    try:
        obligations_result = analyze_contract_for_obligations(
            contract_text=contract_text,
            guidelines_path=guidelines_path,
            save_to_file=True,
            output_dir="temp_uploads"
        )
        results['obligations_analysis'] = json.loads(obligations_result)
    except Exception as e:
        st.error(f"Error in obligations analysis: {e}")
        results['obligations_analysis'] = {"error": str(e)}
    
    # Step 3: Table extraction (only for PDF files)
    with progress_placeholder.container():
        if contract_path.lower().endswith('.pdf'):
            display_progress_update("Extracting tables from PDF...", 4, 4)
            time.sleep(1)
            
            try:
                tables_result = analyze_document_and_extract_tables(
                    pdf_path=contract_path,
                    save_to_excel=True,
                    save_to_json=True,
                    output_dir="temp_uploads"
                )
                results['table_extraction'] = json.loads(tables_result)
            except Exception as e:
                st.error(f"Error in table extraction: {e}")
                results['table_extraction'] = {"error": str(e)}
        else:
            display_progress_update("Skipping table extraction (not a PDF file)", 4, 4)
            results['table_extraction'] = {
                "message": "Table extraction is only available for PDF files",
                "file_type": "text"
            }
    
    progress_placeholder.empty()
    return results

def main():
    """Main Streamlit application"""
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">📄 Contract Analysis AI Agent</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Sidebar for file uploads
    with st.sidebar:
        st.header("📁 Upload Files")
        
        # Contract file upload
        st.subheader("1. Contract File")
        contract_file = st.file_uploader(
            "Upload contract file",
            type=['pdf', 'txt'],
            help="Upload your contract in PDF or TXT format"
        )
        
        if contract_file:
            file_extension = Path(contract_file.name).suffix.lower()
            if file_extension == '.txt':
                st.markdown("""
                <div class="warning-message">
                    <strong>Note:</strong> TXT files uploaded - Table extraction will be skipped.
                </div>
                """, unsafe_allow_html=True)
        
        # Template type input
        st.subheader("2. Contract Template Type")
        template_name = st.text_input(
            "Enter template type",
            placeholder="e.g., MSA, Statement of Work, NDA",
            help="Specify the type of contract template for field extraction"
        )
        
        # Guidelines file upload
        st.subheader("3. Guidelines File")
        guidelines_file = st.file_uploader(
            "Upload guidelines JSON file",
            type=['json'],
            help="Upload the JSON file containing obligation extraction guidelines"
        )
        
        if guidelines_file:
            is_valid, message = validate_json_file(guidelines_file)
            if is_valid:
                st.success("✅ Valid JSON file uploaded")
            else:
                st.error(f"❌ {message}")
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("🚀 Analysis Control")
        
        # Check if all required files are uploaded
        all_files_uploaded = contract_file is not None and guidelines_file is not None and template_name.strip()
        
        if not all_files_uploaded:
            missing_items = []
            if not contract_file:
                missing_items.append("Contract file")
            if not guidelines_file:
                missing_items.append("Guidelines JSON file")
            if not template_name.strip():
                missing_items.append("Template type")
            
            st.warning(f"Please upload/provide: {', '.join(missing_items)}")
        
        # Analysis button
        analyze_button = st.button(
            "🔍 Start Analysis",
            disabled=not all_files_uploaded or st.session_state.analysis_in_progress,
            use_container_width=True
        )
        
        if analyze_button and all_files_uploaded:
            st.session_state.analysis_in_progress = True
            st.session_state.analysis_complete = False
            
            # Save uploaded files
            try:
                contract_path = save_uploaded_file(contract_file)
                guidelines_path = save_uploaded_file(guidelines_file)
                cleaned_template_name = clean_template_name(template_name)
                
                st.session_state.uploaded_files = {
                    'contract_path': contract_path,
                    'guidelines_path': guidelines_path,
                    'template_name': cleaned_template_name,
                    'contract_filename': contract_file.name
                }
                
                # Run analysis
                with st.spinner("Running contract analysis..."):
                    results = run_contract_analysis(
                        contract_path, 
                        cleaned_template_name, 
                        guidelines_path
                    )
                
                if results:
                    st.session_state.analysis_results = results
                    st.session_state.analysis_complete = True
                    st.markdown("""
                    <div class="success-message">
                        <strong>✅ Analysis Complete!</strong> Check the results in the tabs below.
                    </div>
                    """, unsafe_allow_html=True)
                
            except Exception as e:
                st.error(f"Error during analysis: {e}")
            finally:
                st.session_state.analysis_in_progress = False
    
    with col2:
        st.header("📊 File Status")
        
        # Display upload status
        status_items = [
            ("Contract File", contract_file is not None, contract_file.name if contract_file else "Not uploaded"),
            ("Template Type", bool(template_name.strip()), template_name.strip() if template_name.strip() else "Not specified"),
            ("Guidelines File", guidelines_file is not None, guidelines_file.name if guidelines_file else "Not uploaded")
        ]
        
        for item_name, is_uploaded, filename in status_items:
            status_icon = "✅" if is_uploaded else "❌"
            st.write(f"{status_icon} **{item_name}**: {filename}")
    
    # Results section
    if st.session_state.analysis_complete and st.session_state.analysis_results:
        st.markdown("---")
        st.header("📋 Analysis Results")
        
        # Create tabs for different analysis results
        tab1, tab2, tab3 = st.tabs(["📝 Template Extraction", "⚖️ Obligations Analysis", "📊 Tables"])
        
        results = st.session_state.analysis_results
        
        with tab1:
            st.subheader("Template-Based Field Extraction")
            if 'template_extraction' in results:
                template_data = results['template_extraction']
                if 'error' in template_data:
                    st.error(f"Error in template extraction: {template_data['error']}")
                else:
                    if 'result' in template_data:
                        st.json(template_data['result'])
                        
                        # Display as formatted table if possible
                        try:
                            df = pd.DataFrame([template_data['result']])
                            st.dataframe(df, use_container_width=True)
                        except:
                            pass
                    else:
                        st.json(template_data)
        
        with tab2:
            st.subheader("Contract Obligations Analysis")
            if 'obligations_analysis' in results:
                obligations_data = results['obligations_analysis']
                if 'error' in obligations_data:
                    st.error(f"Error in obligations analysis: {obligations_data['error']}")
                else:
                    if 'result' in obligations_data:
                        obligations_list = obligations_data['result']
                        
                        if isinstance(obligations_list, list) and obligations_list:
                            # Display as expandable sections
                            for i, obligation in enumerate(obligations_list):
                                with st.expander(f"Obligation {i+1}: {obligation.get('category', 'Unknown Category')}"):
                                    st.write(f"**Text:** {obligation.get('text', 'N/A')}")
                                    st.write(f"**Category:** {obligation.get('category', 'N/A')}")
                                    if 'additional_info' in obligation:
                                        st.write(f"**Additional Info:** {obligation['additional_info']}")
                            
                            # Also show as dataframe
                            try:
                                df = pd.DataFrame(obligations_list)
                                st.dataframe(df, use_container_width=True)
                            except:
                                st.json(obligations_list)
                        else:
                            st.json(obligations_data)
                    else:
                        st.json(obligations_data)
        
        with tab3:
            st.subheader("Table Extraction")
            if 'table_extraction' in results:
                table_data = results['table_extraction']
                
                if 'message' in table_data and table_data.get('file_type') == 'text':
                    st.info("📝 Table extraction is only available for PDF files. Your uploaded file was in text format.")
                elif 'error' in table_data:
                    st.error(f"Error in table extraction: {table_data['error']}")
                else:
                    if 'result' in table_data and 'tables' in table_data['result']:
                        tables_info = table_data['result']['tables']
                        
                        if tables_info:
                            st.success(f"Found {len(tables_info)} page(s) with tables")
                            
                            for page_data in tables_info:
                                page_num = page_data.get('page_number', 'Unknown')
                                tables = page_data.get('tables', [])
                                
                                st.subheader(f"Page {page_num}")
                                
                                for table_idx, table in enumerate(tables):
                                    with st.expander(f"Table {table_idx + 1} ({table.get('rows', 0)} rows × {table.get('columns', 0)} columns)"):
                                        table_data_content = table.get('data', [])
                                        
                                        if table_data_content:
                                            try:
                                                # Convert to DataFrame for better display
                                                if len(table_data_content) > 1:
                                                    df = pd.DataFrame(table_data_content[1:], columns=table_data_content[0])
                                                else:
                                                    df = pd.DataFrame(table_data_content)
                                                
                                                st.dataframe(df, use_container_width=True)
                                            except:
                                                # Fallback to raw display
                                                for row_idx, row in enumerate(table_data_content):
                                                    st.write(f"Row {row_idx + 1}: {row}")
                                        else:
                                            st.write("No data in this table")
                        else:
                            st.info("No tables found in the document")
                    else:
                        st.json(table_data)

if __name__ == "__main__":
    main()
